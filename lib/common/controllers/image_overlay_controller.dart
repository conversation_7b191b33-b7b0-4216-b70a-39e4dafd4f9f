import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/controllers/setting_controller.dart';
import 'package:nanami_flutter/common/services/image.dart';
import 'package:nanami_flutter/common/services/image_group.dart';
import 'package:nanami_flutter/common/utils/human_face_detect.dart';
import 'package:nanami_flutter/common/utils/logger.dart';
import 'package:nanami_flutter/common/utils/path.dart';
import 'package:nanami_flutter/pages/tab_pages/image/sheets/select_multiple_images_sheet.dart';
import 'package:nanami_flutter/pages/tab_pages/image_group/sheets/select_image_group_sheet.dart';
import 'package:nanami_flutter/widgets/overlay_image_tile_item.dart';
import 'package:nanami_flutter/widgets/overlay_watermark_tile_item.dart';
import 'package:open_file_manager/open_file_manager.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:toastification/toastification.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:lottie/lottie.dart';
import 'package:nanami_flutter/lib/image_blend/services/image_blend_service.dart';
import 'package:nanami_flutter/lib/image_blend/models/blend_models.dart'
    as blend_models;

// 图片贴膜页面控制器
class ImageOverlayController extends GetxController {
  @override
  void onInit() {
    super.onInit();
    // 添加滚动器监听
    // scrollController.addListener(
    //   () => LoggerUtil.getLogger.d("滚动位置: ${scrollController.offset}"),
    // );
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }

  // 获取设置控制器
  final SettingController _settingController = Get.find<SettingController>();

  final ScrollController scrollController = ScrollController();

  // 用于控制底图AnimatedList的全局Key
  final GlobalKey<AnimatedListState> baseImgListKey =
      GlobalKey<AnimatedListState>();

  // 用于控制水印AnimatedList的全局Key
  final GlobalKey<AnimatedListState> watermarkListKey =
      GlobalKey<AnimatedListState>();

  // 记录底图和水印列表的滚动位置
  double baseImgScrollOffset = 0.0;
  double watermarkScrollOffset = 0.0;

  // 当前选中的分段索引
  final RxInt selectedSegment = 0.obs;
  // 图片列表
  final RxList<OverlayImage> overlayImages = <OverlayImage>[].obs;
  // 水印列表
  final RxList<OverlayWatermark> overlayWatermarks = <OverlayWatermark>[].obs;

  // 批量识别状态
  final RxBool isBatchDetecting = false.obs;
  // 批量识别进度
  final RxInt batchDetectProgress = 0.obs;
  // 批量识别百分比进度
  final RxDouble batchDetectProgressPercent = 0.0.obs;
  // 批量识别成功数量
  final RxInt batchDetectSuccessCount = 0.obs;
  // 批量识别失败数量
  final RxInt batchDetectFailCount = 0.obs;

  // 选中的底图列表计算属性
  List<OverlayImage> get selectedImages =>
      overlayImages.where((image) => image.checked.value).toList();

  // 所有底图资产实体的计算属性
  List<AssetEntity> get imageAssetEntites =>
      overlayImages
          .where((image) => image.assetEntity != null)
          .map((image) => image.assetEntity!)
          .toList();

  // 所有水印资产实体的计算属性
  List<AssetEntity> get watermarkAssetEntites =>
      overlayWatermarks.map((watermark) => watermark.assetEntity).toList();

  // 所有底图 id 的计算属性
  List<int> get baseImageIds =>
      overlayImages
          .where((image) => image.id != null)
          .map((image) => image.id!)
          .toList();

  // 检查图片是否已经添加到底图列表
  bool isImageAdded(int imageId) {
    return baseImageIds.contains(imageId);
  }

  // 切换分段
  void changeSegment(int? value) {
    if (value != null && value != selectedSegment.value) {
      // 保存当前滚动位置
      if (scrollController.hasClients) {
        if (selectedSegment.value == 0) {
          // 当前是底图列表，保存底图滚动位置
          baseImgScrollOffset = scrollController.offset;
        } else {
          // 当前是水印列表，保存水印滚动位置
          watermarkScrollOffset = scrollController.offset;
        }
      }

      // 打印出当前的滚动位置
      LoggerUtil.logger.d("当前滚动位置: ${scrollController.offset}");

      // 当滚动 offset 到达 56 这个阈值时候，会切换到小标题

      // 更新选中的分段
      selectedSegment.value = value;

      // Future.microtask(() {
      //   // 恢复目标列表的滚动位置
      //   if (scrollController.hasClients) {
      //     if (value == 0) {
      //       // 切换到底图列表，恢复底图滚动位置
      //       scrollController.jumpTo(56);
      //     } else {
      //       // 切换到水印列表，恢复水印滚动位置
      //       scrollController.jumpTo(56);
      //     }
      //   }
      // });

      if (scrollController.hasClients) {
        // 获取当前滚动位置
        final currentOffset = scrollController.offset;

        // 确定目标滚动位置
        double targetOffset;
        if (value == 0) {
          // 切换到底图列表
          targetOffset = baseImgScrollOffset;
        } else {
          // 切换到水印列表
          targetOffset = watermarkScrollOffset;
        }

        // 如果当前滚动位置大于等于56，且目标滚动位置小于56，则跳转到56的位置
        // （实现了如果当前是小标题模式，切换到另一个列表时，继续保持小标题模式）
        if (currentOffset >= 56 && targetOffset < 56) {
          scrollController.jumpTo(56);
        }
        // 如果当前滚动位置小于56（处于大标题模式），且目标滚动位置大于等于56（处于小标题模式），则跳转到0的位置
        // （实现了如果当前是大标题模式，切换到另一个列表时，返回顶部，保持大标题模式）
        else if (currentOffset < 56 && targetOffset >= 56) {
          scrollController.jumpTo(0);
        } else {
          // 否则跳转到原来保存的位置
          scrollController.jumpTo(targetOffset);
        }
      }
    }
  }

  // 从设备添加底图
  void addBaseImageFromDevice(BuildContext context) async {
    LoggerUtil.logger.d("触发添加底图方法");

    final List<AssetEntity>? assetEntities = await AssetPicker.pickAssets(
      context,
      pickerConfig: AssetPickerConfig(
        selectedAssets: imageAssetEntites,
        maxAssets: 500,
        requestType: RequestType.image,
        pathNameBuilder: (AssetPathEntity path) {
          // 处理特殊相册名称
          if (path.isAll) {
            return '所有图片';
          }
          if (path.name == 'Camera') {
            return '相机';
          }
          if (path.name == 'Screenshots') {
            return '截屏';
          }
          if (path.name == 'Download') {
            return '下载';
          }
          if (path.name == ('WeChat') || path.name == ('WeiXin')) {
            return '微信';
          }
          if (path.name == ('douyin')) {
            return '抖音';
          }
          if (path.name == ('bili')) {
            return '哔哩哔哩';
          }
          // 默认返回原始名称
          return path.name;
        },
      ),
    );

    // 将获取到的资产添加进图片列表
    if (assetEntities != null) {
      for (var asset in assetEntities) {
        // 如果 asset 不存在于 imageAssetEntites 中，则添加
        if (!imageAssetEntites.contains(asset)) {
          await addOverlayImageforDevice(asset);
        }
      }
    }
  }

  // 添加底图
  Future<void> addOverlayImageforDevice(AssetEntity assetEntity) async {
    LoggerUtil.logger.d("添加底图");
    // 获取原始文件
    File? file = await assetEntity.loadFile();
    if (file == null) {
      // 处理文件加载失败的情况
      return;
    }

    // 获取文件名（不带扩展名）
    String nameWithoutExtension = path.basenameWithoutExtension(file.path);

    // 创建底图对象
    final overlayImage = OverlayImage(
      assetEntity: assetEntity,
      name: nameWithoutExtension,
      filePath: file.path,
      maskFilePath: Rxn<String>(),
    );

    // 获取当前列表长度作为插入位置
    int insertIndex = overlayImages.length;

    // 将底图添加到列表中
    overlayImages.add(overlayImage);

    // 触发AnimatedList的插入动画
    baseImgListKey.currentState?.insertItem(insertIndex);
  }

  // 根据索引删除底图
  void removeOverlayImageByIndex(int index) {
    // 检查索引是否有效
    if (index < 0 || index >= overlayImages.length) {
      LoggerUtil.logger.d("删除底图失败：索引无效 $index");
      return;
    }

    LoggerUtil.logger.d("删除底图，索引: $index");

    // 保存要删除的底图对象
    final removedItem = overlayImages[index];

    // 从列表中移除底图
    overlayImages.removeAt(index);

    // 触发AnimatedList的移除动画
    baseImgListKey.currentState?.removeItem(
      index,
      (context, animation) =>
          _buildRemovedItem(removedItem, context, animation),
    );
  }

  // 构建被移除项的动画效果
  Widget _buildRemovedItem(
    OverlayImage item,
    BuildContext context,
    Animation<double> animation,
  ) {
    // 使用FadeTransition和SizeTransition组合动画效果
    return SizeTransition(
      sizeFactor: animation,
      child: FadeTransition(
        opacity: animation,
        child: Column(
          children: [
            // 构建与列表项相似的视图，以实现平滑的删除动画
            OverlayImageTileItem(
              title: item.name,
              imagePath: item.filePath,
              maskPath: item.maskFilePath.value,
              maskBlendMode: _settingController.showMaskBlendMode.value,
              maskOpacity: _settingController.showMaskOpacity.value,
              checked: item.checked.value,
              onImageTap: null,
              onDelete: null,
              onChanged: null,
            ),
            // 分割线
            Divider(
              height: 1,
              color: CupertinoColors.systemGroupedBackground,
              indent: 97.0,
              endIndent: 15.0,
            ),
          ],
        ),
      ),
    );
  }

  // 清空所有底图
  void clearOverlayImages() {
    LoggerUtil.logger.d("清空所有底图");

    // 如果列表为空，直接返回
    if (overlayImages.isEmpty) return;

    // 逐个移除项目以触发动画
    for (int i = overlayImages.length - 1; i >= 0; i--) {
      final removedItem = overlayImages[i];
      overlayImages.removeAt(i);

      baseImgListKey.currentState?.removeItem(
        i,
        (context, animation) =>
            _buildRemovedItem(removedItem, context, animation),
      );
    }
  }

  // 从设备添加水印
  void addWatermarkFromDevice(BuildContext context) async {
    LoggerUtil.logger.d("触发添加水印方法");

    final List<AssetEntity>? assetEntities = await AssetPicker.pickAssets(
      context,
      pickerConfig: AssetPickerConfig(
        selectedAssets: watermarkAssetEntites,
        maxAssets: 99,
        requestType: RequestType.image,
        pathNameBuilder: (AssetPathEntity path) {
          // 处理特殊相册名称
          if (path.isAll) {
            return '所有图片';
          }
          if (path.name == 'Camera') {
            return '相机';
          }
          if (path.name == 'Screenshots') {
            return '截屏';
          }
          if (path.name == 'Download') {
            return '下载';
          }
          if (path.name == ('WeChat') || path.name == ('WeiXin')) {
            return '微信';
          }
          if (path.name == ('douyin')) {
            return '抖音';
          }
          if (path.name == ('bili')) {
            return '哔哩哔哩';
          }
          // 默认返回原始名称
          return path.name;
        },
      ),
    );

    // 将获取到的资产添加进水印列表
    if (assetEntities != null) {
      for (var asset in assetEntities) {
        // 如果 asset 不存在于 watermarkAssetEntites 中，则添加
        if (!watermarkAssetEntites.contains(asset)) {
          await addOverlayWatermark(asset);
        }
      }
    }
  }

  // 添加水印
  Future<void> addOverlayWatermark(AssetEntity assetEntity) async {
    LoggerUtil.logger.d("添加水印");
    // 获取原始文件
    File? file = await assetEntity.loadFile();
    if (file == null) {
      // 处理文件加载失败的情况
      return;
    }

    // 获取文件名（不带扩展名）
    String nameWithoutExtension = path.basenameWithoutExtension(file.path);

    // 创建水印对象
    final overlayWatermark = OverlayWatermark(
      assetEntity: assetEntity,
      filePath: file.path.obs,
      name: nameWithoutExtension.obs,
    );

    // 获取当前列表长度作为插入位置
    int insertIndex = overlayWatermarks.length;

    // 将水印添加到列表中
    overlayWatermarks.add(overlayWatermark);

    // 触发AnimatedList的插入动画
    watermarkListKey.currentState?.insertItem(insertIndex);
  }

  // 根据索引删除水印
  void removeOverlayWatermarkByIndex(int index) {
    // 检查索引是否有效
    if (index < 0 || index >= overlayWatermarks.length) {
      LoggerUtil.logger.d("删除水印失败：索引无效 $index");
      return;
    }

    LoggerUtil.logger.d("删除水印，索引: $index");

    // 保存要删除的水印对象
    final removedItem = overlayWatermarks[index];

    // 从列表中移除水印
    overlayWatermarks.removeAt(index);

    // 触发AnimatedList的移除动画
    watermarkListKey.currentState?.removeItem(
      index,
      (context, animation) =>
          _buildRemovedWatermarkItem(removedItem, context, animation),
    );
  }

  // 构建被移除水印项的动画效果
  Widget _buildRemovedWatermarkItem(
    OverlayWatermark item,
    BuildContext context,
    Animation<double> animation,
  ) {
    // 使用FadeTransition和SizeTransition组合动画效果
    return SizeTransition(
      sizeFactor: animation,
      child: FadeTransition(
        opacity: animation,
        child: Column(
          children: [
            // 构建与列表项相似的视图，以实现平滑的删除动画
            OverlayWatermarkTileItem(
              title: item.name.value,
              imagePath: item.filePath.value,
              onImageTap: null,
              onDelete: null,
            ),
            // 分割线
            Divider(
              height: 1,
              color: CupertinoColors.systemGroupedBackground,
              indent: 97.0,
              endIndent: 15.0,
            ),
          ],
        ),
      ),
    );
  }

  // 重命名水印
  void renameWatermark(int index, String newName) {
    // 检查索引是否有效
    if (index < 0 || index >= overlayWatermarks.length) {
      LoggerUtil.logger.d("重命名水印失败：索引无效 $index");
      return;
    }

    // 检查新名称是否为空
    if (newName.trim().isEmpty) {
      LoggerUtil.logger.d("重命名水印失败：新名称为空");
      return;
    }

    LoggerUtil.logger.d("重命名水印，索引: $index，新名称: $newName");

    // 更新水印名称
    overlayWatermarks[index].name.value = newName.trim();
  }

  // 清空所有水印
  void clearOverlayWatermarks() {
    LoggerUtil.logger.d("清空所有水印");

    // 如果列表为空，直接返回
    if (overlayWatermarks.isEmpty) return;

    // 逐个移除项目以触发动画
    for (int i = overlayWatermarks.length - 1; i >= 0; i--) {
      final removedItem = overlayWatermarks[i];
      overlayWatermarks.removeAt(i);

      watermarkListKey.currentState?.removeItem(
        i,
        (context, animation) =>
            _buildRemovedWatermarkItem(removedItem, context, animation),
      );
    }
  }

  // 从图库添加底图
  Future<void> addBaseImageFromGallery(BuildContext context) async {
    LoggerUtil.logger.d("从图库添加底图");

    // 调用选择图片的 Sheet
    final List<int>? selectedImageIds = await showSelectMultipleImagesSheet(
      context,
    );

    // 如果用户取消了选择，直接返回
    if (selectedImageIds == null || selectedImageIds.isEmpty) {
      return;
    }

    // 导入选中的图片
    await _importImagesById(selectedImageIds);
  }

  // 从相簿添加底图
  Future<void> addBaseImageFromImageGroup(BuildContext context) async {
    LoggerUtil.logger.d("从相簿添加底图");

    // 调用选择相簿的 Sheet
    final int? selectedGroupId = await showSelectImageGroupSheet(context);

    // 如果用户取消了选择，直接返回
    if (selectedGroupId == null) {
      return;
    }

    // 获取相簿详情
    final imageGroupInfo = await ImageGroupService.getById(selectedGroupId);

    // 如果相簿不存在或没有图片，直接返回
    if (imageGroupInfo == null || imageGroupInfo.images.isEmpty) {
      return;
    }

    // 获取相簿中所有图片的 ID
    final List<int> imageIds =
        imageGroupInfo.images.map((image) => image.id).toList();

    // 导入相簿中的图片
    await _importImagesById(imageIds);
  }

  // 根据图片 ID 导入图片
  Future<void> _importImagesById(List<int> imageIds) async {
    // 遍历所有图片 ID
    for (final imageId in imageIds) {
      // 检查图片是否已经添加到底图列表
      if (isImageAdded(imageId)) {
        LoggerUtil.logger.d("图片已存在，跳过导入，ID: $imageId");
        continue;
      }

      // 获取图片详情
      final imageInfo = await ImageService.getById(imageId);

      // 如果图片不存在，跳过
      if (imageInfo == null) {
        LoggerUtil.logger.d("图片不存在，跳过导入，ID: $imageId");
        continue;
      }

      // 创建底图对象
      final overlayImage = OverlayImage(
        id: imageId,
        name: imageInfo.name.value,
        filePath: imageInfo.path,
        maskFilePath:
            imageInfo.maskPath != null
                ? Rxn<String>(imageInfo.maskPath)
                : Rxn<String>(),
      );

      // 获取当前列表长度作为插入位置
      int insertIndex = overlayImages.length;

      // 将底图添加到列表中
      overlayImages.add(overlayImage);

      // 触发AnimatedList的插入动画
      baseImgListKey.currentState?.insertItem(insertIndex);
    }
  }

  // 识别真人脸部(单张图片)
  Future<void> detectHumanFace(
    BuildContext context,
    OverlayImage overlayImage,
  ) async {
    try {
      final filePath = overlayImage.filePath;
      if (filePath.isEmpty) {
        toastification.show(
          type: ToastificationType.error,
          style: ToastificationStyle.flat,
          title: Text("识别失败"),
          description: Text("图片路径无效"),
          alignment: Alignment.topCenter,
          autoCloseDuration: const Duration(seconds: 2),
          dragToClose: true,
        );
        return;
      }

      // 调用优化后的人脸检测方法（现在在独立Isolate中执行）
      String? path = await HumanFaceDetectUtils.getAutoFaceContoursMask(
        filePath,
        expansionRate: _settingController.faceDetectExpansionRate.value,
      );

      // 只有在成功识别到人脸时才设置蒙版路径
      if (path != null) {
        overlayImage.maskFilePath.value = path;
        toastification.show(
          type: ToastificationType.warning,
          style: ToastificationStyle.flat,
          icon: Lottie.asset(
            "assets/lotties/face_detect_successful.json",
            frameRate: FrameRate.max,
            width: 50,
            height: 50,
            repeat: false,
            delegates: LottieDelegates(
              values: [
                // 使用 ColorFilter 修改所有颜色
                ValueDelegate.colorFilter(
                  const ['**'], // 匹配所有图层
                  value: ColorFilter.mode(Colors.green, BlendMode.multiply),
                ),
              ],
            ),
          ),
          title: Text("识别成功"),
          description: Text("已标注脸部"),
          alignment: Alignment.topCenter,
          autoCloseDuration: const Duration(seconds: 2),
          dragToClose: true,
        );
      } else {
        toastification.show(
          type: ToastificationType.warning,
          style: ToastificationStyle.flat,
          icon: Lottie.asset(
            "assets/lotties/face_detect_failed.json",
            frameRate: FrameRate.max,
            width: 50,
            height: 50,
            repeat: false,
            delegates: LottieDelegates(
              values: [
                // 使用 ColorFilter 修改所有颜色
                ValueDelegate.colorFilter(
                  const ['**'], // 匹配所有图层
                  value: ColorFilter.mode(
                    Colors.orangeAccent,
                    BlendMode.multiply,
                  ),
                ),
              ],
            ),
          ),
          title: Text("识别失败"),
          description: Text("未检测到人脸"),
          alignment: Alignment.topCenter,
          autoCloseDuration: const Duration(seconds: 2),
          dragToClose: true,
        );
      }
    } catch (e) {
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flat,
        title: Text("识别失败"),
        description: Text("出现错误：$e"),
        alignment: Alignment.topCenter,
        autoCloseDuration: const Duration(seconds: 2),
        dragToClose: true,
      );
    }
  }

  // 打开输出目录
  void openOutputDir() {
    openFileManager(
      androidConfig: AndroidConfig(
        folderType: AndroidFolderType.other,
        folderPath:
            // '/storage/emulated/0/Android/data/com.nanami.mobile/files/贴膜输出',
            PathUtils.overlayOutputDir,
      ),
      iosConfig: IosConfig(
        // Path is case-sensitive here.
        folderPath: '贴膜输出',
      ),
    );
  }

  // 批量识别所有图片中的人脸
  Future<void> detectAllHumanFaces() async {
    // 如果没有图片，直接返回
    if (overlayImages.isEmpty) {
      toastification.show(
        type: ToastificationType.warning,
        style: ToastificationStyle.flat,
        title: Text("未添加底图"),
        description: Text("请先添加底图"),
        alignment: Alignment.topCenter,
        autoCloseDuration: const Duration(seconds: 2),
        dragToClose: true,
      );
      return;
    }

    // 如果正在进行批量识别，直接返回
    if (isBatchDetecting.value) {
      return;
    }

    // 重置状态
    batchDetectProgress.value = 0;
    batchDetectSuccessCount.value = 0;
    batchDetectFailCount.value = 0;
    batchDetectProgressPercent.value = 0.0;

    // 设置为正在识别状态
    isBatchDetecting.value = true;

    try {
      // 获取图片总数
      final int totalCount = overlayImages.length;

      // 遍历所有图片，逐个进行人脸识别
      for (int i = 0; i < totalCount; i++) {
        final OverlayImage overlayImage = overlayImages[i];
        final String filePath = overlayImage.filePath;

        if (filePath.isEmpty) {
          batchDetectFailCount.value++;
          continue;
        }

        // 调用优化后的人脸检测方法
        String? path = await HumanFaceDetectUtils.getAutoFaceContoursMask(
          filePath,
          expansionRate: _settingController.faceDetectExpansionRate.value,
        );

        // 更新进度
        batchDetectProgress.value = i + 1;
        // 计算百分比进度
        batchDetectProgressPercent.value = ((i + 1) / totalCount) * 100;

        // 根据识别结果更新计数
        if (path != null) {
          // 识别成功
          overlayImage.maskFilePath.value = path;
          batchDetectSuccessCount.value++;
        } else {
          // 识别失败
          batchDetectFailCount.value++;
        }
      }

      // 根据识别结果显示不同类型的 toast
      if (batchDetectFailCount.value == 0) {
        // 全部识别成功
        toastification.show(
          type: ToastificationType.success,
          style: ToastificationStyle.flat,
          title: Text("全部识别成功"),
          description: Text("共识别了 ${batchDetectSuccessCount.value} 张图片"),
          alignment: Alignment.topCenter,
          autoCloseDuration: const Duration(seconds: 3),
          dragToClose: true,
        );
      } else {
        // 部分识别失败
        toastification.show(
          type: ToastificationType.warning,
          style: ToastificationStyle.flat,
          title: Text("部分识别成功"),
          description: Text(
            "成功 ${batchDetectSuccessCount.value} 张，未识别到 ${batchDetectFailCount.value} 张",
          ),
          alignment: Alignment.topCenter,
          autoCloseDuration: const Duration(seconds: 3),
          dragToClose: true,
        );
      }
    } catch (e) {
      // 显示错误提示
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flat,
        title: Text("识别失败"),
        description: Text("出现错误：$e"),
        alignment: Alignment.topCenter,
        autoCloseDuration: const Duration(seconds: 3),
        dragToClose: true,
      );
    } finally {
      // 无论成功还是失败，都将状态设置为非识别状态
      isBatchDetecting.value = false;
    }
  }

  // 贴膜相关状态
  final RxBool isProcessing = false.obs;
  final RxInt processProgress = 0.obs;
  final RxDouble processProgressPercent = 0.0.obs;
  final RxInt processSuccessCount = 0.obs;
  final RxInt processFailCount = 0.obs;

  /// 开始贴膜处理
  Future<void> startOverlayProcess() async {
    LoggerUtil.logger.d("开始贴膜处理被调用");
    LoggerUtil.logger.d("底图数量: ${overlayImages.length}");
    LoggerUtil.logger.d("选中底图数量: ${selectedImages.length}");
    LoggerUtil.logger.d("水印数量: ${overlayWatermarks.length}");

    // 检查是否有底图和水印
    LoggerUtil.logger.d("开始检查底图和水印");
    if (selectedImages.isEmpty) {
      LoggerUtil.logger.d("没有选中的底图，显示警告");
      toastification.show(
        type: ToastificationType.warning,
        style: ToastificationStyle.flat,
        title: Text("未选择底图"),
        description: Text("请先选择要处理的底图"),
        alignment: Alignment.topCenter,
        autoCloseDuration: const Duration(seconds: 2),
        dragToClose: true,
      );
      return;
    }

    if (overlayWatermarks.isEmpty) {
      LoggerUtil.logger.d("没有水印，显示警告");
      toastification.show(
        type: ToastificationType.warning,
        style: ToastificationStyle.flat,
        title: Text("未添加水印"),
        description: Text("请先添加水印"),
        alignment: Alignment.topCenter,
        autoCloseDuration: const Duration(seconds: 2),
        dragToClose: true,
      );
      return;
    }

    // 如果正在处理，直接返回
    if (isProcessing.value) {
      LoggerUtil.logger.d("正在处理中，直接返回");
      return;
    }

    LoggerUtil.logger.d("检查通过，开始处理");

    // 重置状态
    LoggerUtil.logger.d("重置状态");
    processProgress.value = 0;
    processSuccessCount.value = 0;
    processFailCount.value = 0;
    processProgressPercent.value = 0.0;

    // 设置为正在处理状态
    LoggerUtil.logger.d("设置为正在处理状态");
    isProcessing.value = true;

    try {
      LoggerUtil.logger.d("开始准备参数列表");
      // 准备参数列表
      final List<blend_models.OverlayImageParams> paramsList = [];

      LoggerUtil.logger.d("开始遍历底图和水印");
      for (final baseImage in selectedImages) {
        LoggerUtil.logger.d("处理底图: ${baseImage.name}");
        for (final watermark in overlayWatermarks) {
          LoggerUtil.logger.d("处理水印: ${watermark.name.value}");
          // 生成输出文件名
          final outputFileName =
              '${baseImage.name}_${watermark.name.value}.png';
          LoggerUtil.logger.d("生成输出文件名: $outputFileName");

          LoggerUtil.logger.d("获取输出路径");
          final outputPath = await _getOutputPath(outputFileName);
          LoggerUtil.logger.d("输出路径: $outputPath");

          final params = blend_models.OverlayImageParams(
            baseImagePath: baseImage.filePath,
            topImagePath: watermark.filePath.value,
            outputPath: outputPath,
            blendMode: _stringToBlendMode(
              _settingController.overlayBlendMode.value,
            ),
            tilingMode: _stringToTilingMode(
              _settingController.overlayFillMode.value,
            ),
            opacity: _settingController.overlayOpacity.value,
            tilingScale: _settingController.overlayTileRate.value,
            maskImagePath: baseImage.maskFilePath.value,
          );

          paramsList.add(params);
        }
      }

      // 使用 Isolate 进行批量处理
      final imageBlendService = ImageBlendService();
      await imageBlendService.batchOverlayImagesWithProgress(
        paramsList,
        useIsolate: true, // 使用 Isolate
        onProgress: (current, total, percentage) {
          processProgress.value = current;
          processProgressPercent.value = percentage;
        },
        onStatusUpdate: (successCount, failCount) {
          processSuccessCount.value = successCount;
          processFailCount.value = failCount;
        },
      );

      // 处理完成提示
      if (processFailCount.value == 0) {
        // 全部成功
        toastification.show(
          type: ToastificationType.success,
          style: ToastificationStyle.flat,
          title: Text("贴膜完成"),
          description: Text("成功处理 ${processSuccessCount.value} 张图片"),
          alignment: Alignment.topCenter,
          autoCloseDuration: const Duration(seconds: 3),
          dragToClose: true,
        );
      } else {
        // 部分失败
        toastification.show(
          type: ToastificationType.warning,
          style: ToastificationStyle.flat,
          title: Text("贴膜完成"),
          description: Text(
            "成功 ${processSuccessCount.value} 张，失败 ${processFailCount.value} 张",
          ),
          alignment: Alignment.topCenter,
          autoCloseDuration: const Duration(seconds: 3),
          dragToClose: true,
        );
      }
    } catch (e) {
      // 显示错误提示
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flat,
        title: Text("贴膜失败"),
        description: Text("出现错误：$e"),
        alignment: Alignment.topCenter,
        autoCloseDuration: const Duration(seconds: 3),
        dragToClose: true,
      );
    } finally {
      // 无论成功还是失败，都将状态设置为非处理状态
      isProcessing.value = false;
    }
  }

  /// 获取输出文件路径
  Future<String> _getOutputPath(String fileName) async {
    try {
      LoggerUtil.logger.d("开始获取输出路径，文件名: $fileName");
      // 这里需要根据你的设置获取输出目录
      // 暂时使用临时目录作为示例
      LoggerUtil.logger.d("获取临时目录");
      final directory = await getTemporaryDirectory();
      LoggerUtil.logger.d("临时目录路径: ${directory.path}");

      final outputDir = Directory('${directory.path}/overlay_output');
      LoggerUtil.logger.d("输出目录路径: ${outputDir.path}");

      if (!await outputDir.exists()) {
        LoggerUtil.logger.d("输出目录不存在，创建目录");
        await outputDir.create(recursive: true);
        LoggerUtil.logger.d("目录创建完成");
      } else {
        LoggerUtil.logger.d("输出目录已存在");
      }

      final fullPath = '${outputDir.path}/$fileName';
      LoggerUtil.logger.d("完整输出路径: $fullPath");
      return fullPath;
    } catch (e) {
      LoggerUtil.logger.e("获取输出路径时出错: $e");
      rethrow;
    }
  }

  /// 将字符串转换为BlendMode枚举
  blend_models.BlendMode _stringToBlendMode(String mode) {
    switch (mode) {
      case 'normal':
        return blend_models.BlendMode.normal;
      case 'overlay':
        return blend_models.BlendMode.overlay;
      case 'soft_light':
        return blend_models.BlendMode.softLight;
      case 'screen':
        return blend_models.BlendMode.screen;
      case 'multiply':
        return blend_models.BlendMode.multiply;
      default:
        return blend_models.BlendMode.normal;
    }
  }

  /// 将字符串转换为TilingMode枚举
  blend_models.TilingMode _stringToTilingMode(String mode) {
    switch (mode) {
      case 'stretch':
        return blend_models.TilingMode.stretch;
      case 'tile':
        return blend_models.TilingMode.tile;
      default:
        return blend_models.TilingMode.stretch;
    }
  }
}

// 贴膜图片类
class OverlayImage {
  // 是否选择
  final RxBool checked = true.obs;
  // 图片编号（从本应用导入专属）
  final int? id;
  // 资产实体（从设备导入专属）
  final AssetEntity? assetEntity;
  // 图片名称
  final String name;
  // 路径
  final String filePath;
  // 遮罩路径
  final Rxn<String> maskFilePath;

  // 构造函数
  OverlayImage({
    this.id,
    this.assetEntity,
    required this.name,
    required this.filePath,
    required this.maskFilePath,
  });
}

// 贴膜水印类
class OverlayWatermark {
  // 资产实体
  final AssetEntity assetEntity;
  // 路径
  final RxString filePath;
  // 水印名称
  final RxString name;

  // 构造函数
  OverlayWatermark({
    required this.assetEntity,
    required this.filePath,
    required this.name,
  });
}
