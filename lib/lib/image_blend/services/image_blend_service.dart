/// 图像混合服务 - 高级封装和便捷方法

import 'dart:io';
import 'package:flutter/foundation.dart';
import '../image_blend_ffi.dart';
import '../models/blend_models.dart';

/// 图像混合服务类
class ImageBlendService {
  static ImageBlendService? _instance;

  /// 单例模式
  factory ImageBlendService() {
    return _instance ??= ImageBlendService._internal();
  }

  ImageBlendService._internal();

  /// 使用 compute 执行图像叠加（推荐用于 Flutter UI）
  Future<FFIResult> overlayImages({
    required String baseImagePath,
    required String topImagePath,
    required String outputPath,
    BlendMode blendMode = BlendMode.normal,
    TilingMode tilingMode = TilingMode.stretch,
    int opacity = 100,
    int tilingScale = 100,
    String? maskImagePath,
  }) async {
    final params = OverlayImageParams(
      baseImagePath: baseImagePath,
      topImagePath: topImagePath,
      outputPath: outputPath,
      blendMode: blendMode,
      tilingMode: tilingMode,
      opacity: opacity,
      tilingScale: tilingScale,
      maskImagePath: maskImagePath,
    );

    // 验证文件是否存在
    final validationResult = await _validateInputFiles([
      baseImagePath,
      topImagePath,
      if (maskImagePath != null) maskImagePath,
    ]);

    if (!validationResult.isSuccess) {
      return validationResult;
    }

    // 使用 compute 在后台线程执行
    return await compute(_overlayImagesCompute, params);
  }

  /// 使用 Isolate 执行图像叠加（适用于长时间运行的任务）
  Future<FFIResult> overlayImagesWithIsolate({
    required String baseImagePath,
    required String topImagePath,
    required String outputPath,
    BlendMode blendMode = BlendMode.normal,
    TilingMode tilingMode = TilingMode.stretch,
    int opacity = 100,
    int tilingScale = 100,
    String? maskImagePath,
  }) async {
    final params = OverlayImageParams(
      baseImagePath: baseImagePath,
      topImagePath: topImagePath,
      outputPath: outputPath,
      blendMode: blendMode,
      tilingMode: tilingMode,
      opacity: opacity,
      tilingScale: tilingScale,
      maskImagePath: maskImagePath,
    );

    // 验证文件是否存在
    final validationResult = await _validateInputFiles([
      baseImagePath,
      topImagePath,
      if (maskImagePath != null) maskImagePath,
    ]);

    if (!validationResult.isSuccess) {
      return validationResult;
    }

    return await ImageBlendAsyncHelper.overlayImagesAsync(params);
  }

  /// 使用 compute 执行图像平铺
  Future<FFIResult> tileImage({
    required String imagePath,
    required String outputPath,
    int tileMultiplier = 2,
  }) async {
    final params = TileImageParams(
      imagePath: imagePath,
      outputPath: outputPath,
      tileMultiplier: tileMultiplier,
    );

    // 验证文件是否存在
    final validationResult = await _validateInputFiles([imagePath]);
    if (!validationResult.isSuccess) {
      return validationResult;
    }

    return await compute(_tileImageCompute, params);
  }

  /// 使用 Isolate 执行图像平铺
  Future<FFIResult> tileImageWithIsolate({
    required String imagePath,
    required String outputPath,
    int tileMultiplier = 2,
  }) async {
    final params = TileImageParams(
      imagePath: imagePath,
      outputPath: outputPath,
      tileMultiplier: tileMultiplier,
    );

    // 验证文件是否存在
    final validationResult = await _validateInputFiles([imagePath]);
    if (!validationResult.isSuccess) {
      return validationResult;
    }

    return await ImageBlendAsyncHelper.tileImageAsync(params);
  }

  /// 使用 compute 执行图像裁剪
  Future<FFIResult> trimImage({
    required String inputPath,
    required String outputPath,
  }) async {
    final params = TrimImageParams(
      inputPath: inputPath,
      outputPath: outputPath,
    );

    // 验证文件是否存在
    final validationResult = await _validateInputFiles([inputPath]);
    if (!validationResult.isSuccess) {
      return validationResult;
    }

    return await compute(_trimImageCompute, params);
  }

  /// 使用 Isolate 执行图像裁剪
  Future<FFIResult> trimImageWithIsolate({
    required String inputPath,
    required String outputPath,
  }) async {
    final params = TrimImageParams(
      inputPath: inputPath,
      outputPath: outputPath,
    );

    // 验证文件是否存在
    final validationResult = await _validateInputFiles([inputPath]);
    if (!validationResult.isSuccess) {
      return validationResult;
    }

    return await ImageBlendAsyncHelper.trimImageAsync(params);
  }

  /// 清理缓存
  Future<FFIResult> clearCache() async {
    return await compute(_clearCacheCompute, null);
  }

  /// 使用 Isolate 清理缓存
  Future<FFIResult> clearCacheWithIsolate() async {
    return await ImageBlendAsyncHelper.clearCacheAsync();
  }

  /// 批量处理图像叠加
  Future<List<FFIResult>> batchOverlayImages(
    List<OverlayImageParams> paramsList, {
    bool useIsolate = false,
  }) async {
    final results = <FFIResult>[];

    for (final params in paramsList) {
      FFIResult result;
      if (useIsolate) {
        result = await ImageBlendAsyncHelper.overlayImagesAsync(params);
      } else {
        result = await compute(_overlayImagesCompute, params);
      }
      results.add(result);
    }

    return results;
  }

  /// 贴膜专用：批量处理图像叠加（带进度回调）
  /// 推荐使用 Isolate 进行大规模批量处理
  Future<List<FFIResult>> batchOverlayImagesWithProgress(
    List<OverlayImageParams> paramsList, {
    bool useIsolate = true, // 默认使用 Isolate
    Function(int current, int total, double percentage)? onProgress,
    Function(int successCount, int failCount)? onStatusUpdate,
  }) async {
    final results = <FFIResult>[];
    int successCount = 0;
    int failCount = 0;

    for (int i = 0; i < paramsList.length; i++) {
      final params = paramsList[i];

      // 验证文件是否存在
      final validationResult = await _validateInputFiles([
        params.baseImagePath,
        params.topImagePath,
        if (params.maskImagePath != null) params.maskImagePath!,
      ]);

      FFIResult result;
      if (!validationResult.isSuccess) {
        result = validationResult;
        failCount++;
      } else {
        // 执行图像叠加
        if (useIsolate) {
          result = await ImageBlendAsyncHelper.overlayImagesAsync(params);
        } else {
          result = await compute(_overlayImagesCompute, params);
        }

        if (result.isSuccess) {
          successCount++;
        } else {
          failCount++;
        }
      }

      results.add(result);

      // 更新进度
      final current = i + 1;
      final percentage = (current / paramsList.length) * 100;
      onProgress?.call(current, paramsList.length, percentage);
      onStatusUpdate?.call(successCount, failCount);
    }

    return results;
  }

  /// 验证输入文件是否存在
  Future<FFIResult> _validateInputFiles(List<String> filePaths) async {
    for (final path in filePaths) {
      final file = File(path);
      if (!await file.exists()) {
        return FFIResult.error(FFIErrorCodes.fileNotFound, '文件不存在: $path');
      }
    }
    return FFIResult.success();
  }

  /// 检查输出目录是否存在，不存在则创建
  Future<void> _ensureOutputDirectory(String outputPath) async {
    final file = File(outputPath);
    final directory = file.parent;
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
  }
}

/// Compute 函数（必须是顶级函数）

/// 在 compute 中执行图像叠加
FFIResult _overlayImagesCompute(OverlayImageParams params) {
  try {
    final ffi = ImageBlendFFI();
    final result = ffi.overlayImagesSync(params);
    return FFIResult(errorCode: result);
  } catch (e) {
    return FFIResult.error(FFIErrorCodes.processingFailed, '图像叠加失败: $e');
  }
}

/// 在 compute 中执行图像平铺
FFIResult _tileImageCompute(TileImageParams params) {
  try {
    final ffi = ImageBlendFFI();
    final result = ffi.tileImageSync(params);
    return FFIResult(errorCode: result);
  } catch (e) {
    return FFIResult.error(FFIErrorCodes.processingFailed, '图像平铺失败: $e');
  }
}

/// 在 compute 中执行图像裁剪
FFIResult _trimImageCompute(TrimImageParams params) {
  try {
    final ffi = ImageBlendFFI();
    final result = ffi.trimImageSync(params);
    return FFIResult(errorCode: result);
  } catch (e) {
    return FFIResult.error(FFIErrorCodes.processingFailed, '图像裁剪失败: $e');
  }
}

/// 在 compute 中执行清理缓存
FFIResult _clearCacheCompute(void _) {
  try {
    final ffi = ImageBlendFFI();
    final result = ffi.clearCacheSync();
    return FFIResult(errorCode: result);
  } catch (e) {
    return FFIResult.error(FFIErrorCodes.processingFailed, '清理缓存失败: $e');
  }
}
